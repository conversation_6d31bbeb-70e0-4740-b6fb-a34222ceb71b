package com.rf.exchange.server.data.core.mq;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.google.common.collect.Lists;
import com.rf.exchange.module.candle.api.ControlPlanApi;
import com.rf.exchange.module.candle.api.dto.ControlPlanDTO;
import com.rf.exchange.module.candle.api.dto.ControlPlanKLineDTO;
import com.rf.exchange.module.candle.service.controlplan.ControlPlanStatusChecker;
import com.rf.exchange.module.candle.api.dto.ControlPricePointDTO;
import com.rf.exchange.module.candle.api.dto.CurrentPriceRespDTO;
import com.rf.exchange.module.candle.api.dto.TodayKlinePriceDTO;
import com.rf.exchange.module.candle.dal.redis.CandleCurrentOrderBookRedisDAO;
import com.rf.exchange.module.candle.dal.redis.CandleCurrentPriceRedisDAO;
import com.rf.exchange.module.candle.dal.redis.CandleCustomTradePairRedisDAO;
import com.rf.exchange.module.candle.dal.redis.dto.CandleOrderBookDTO;
import com.rf.exchange.module.candle.dal.redis.dto.CandleOrderBookListDTO;
import com.rf.exchange.module.exc.api.tradepair.TradePairApi;
import com.rf.exchange.module.exc.api.tradepair.dto.TradePairRespDTO;
import com.rf.exchange.module.exc.dal.redis.TradePairControlRedisDAO;
import com.rf.exchange.server.data.context.TodayKlineHolder;
import com.rf.exchange.server.data.core.marketclose.CandleMarketCloseDataGenerator;
import com.rf.exchange.server.data.core.mq.dto.UpdateDto;
import com.rf.exchange.server.data.core.mq.message.CandlePriceMessage;
import com.rf.exchange.server.data.datasource.alltick.convert.AllTickOrderBookConvert;
import com.rf.exchange.server.data.datasource.alltick.dto.AllTickDataDTO;
import com.rf.exchange.server.data.datasource.alltick.dto.AllTickDealQuoteDTO;
import com.rf.exchange.server.data.datasource.alltick.dto.AllTickPKQuoteDTO;
import com.rf.exchange.server.data.datasource.alltick.util.AllTickUtil;
import com.rf.exchange.server.data.datasource.alltick.websocket.AllTickCommand;
import com.rf.exchange.server.data.datasource.jiangshan.dto.JiangShanPriceDTO;
import com.rf.exchange.server.data.datasource.jiangshan.util.JiangShanTradePairStatusHolder;
import com.rf.exchange.server.data.datasource.jiangshan.util.JiangShanUtil;
import com.rf.exchange.server.data.datasource.polygon.util.PolygonUtil;
import com.rf.exchange.server.data.datasource.polygon.websocket.PolygonConstants;
import com.rf.exchange.server.data.enums.CandleDataSource;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.rf.exchange.server.data.datasource.polygon.websocket.PolygonConstants.*;

@FunctionalInterface
interface QuoteHandleFunction<String, Q, U> {
    void handle(String name, Q quote, U isMarketClose);
}

/**
 * <AUTHOR>
 * @since 2024-07-01
 */
@Slf4j
@Service
@RocketMQMessageListener(topic = "${exchange.candle-rocketmq.topic}",
        consumerGroup = "${exchange.candle-rocketmq.consumer-group}",
        selectorExpression = "${exchange.candle-rocketmq.topic-tag-price} || ${exchange.candle-rocketmq.topic-tag-orderbook}")
public class RocketCandleConsumer implements RocketMQListener<RocketCandleMessage> {

    private static final ObjectMapper MAPPER =
            new ObjectMapper().setPropertyNamingStrategy(new PropertyNamingStrategies.SnakeCaseStrategy());

    /**
     * 交易对的更新信息map 格式：<交易对代码: 更新信息>
     */
    private static final ConcurrentHashMap<String, UpdateDto> CODE_LAST_TICKTIME_MAP = new ConcurrentHashMap<>(100);

    @Resource
    private CandleCurrentPriceRedisDAO priceRedisDAO;
    @Resource
    private CandleCurrentOrderBookRedisDAO orderBookRedisDAO;
    @Resource
    private AppCandlePriceProducer appPriceProducer;
    @Resource
    private TradePairApi tradePairApi;
    @Resource
    private TradePairControlRedisDAO tradePairControlRedisDAO;
    @Resource
    private CandleCustomTradePairRedisDAO candleCustomTradePairRedisDAO;
    @Resource
    private CandleMarketCloseDataGenerator marketCloseDataGenerator;
    @Resource
    private ControlPlanApi controlPlanApi;
    @Resource
    private ControlPlanStatusChecker controlPlanStatusChecker;

    // 控价计划状态缓存，避免频繁查询数据库（已迁移到ControlPlanStatusChecker）
    // private volatile Map<String, Long> controlPlanStatusCache = new ConcurrentHashMap<>();
    // private volatile long lastCacheUpdateTime = 0;

    @Override
    public void onMessage(RocketCandleMessage message) {
        if (message.getDataSource() == CandleDataSource.ALLTICK.getValue()) {
            String payload = message.getPayload();
            if (StrUtil.isNotEmpty(payload)) {
                AllTickDataDTO<Map<String, Object>> dataDto = null;
                try {
                    dataDto = MAPPER.readValue(payload, new TypeReference<>() {
                    });
                } catch (JsonProcessingException | NumberFormatException e) {
                    log.error("Rocket消费candle消息异常 消息转DTO失败: {}", e.getMessage());
                }
                if (dataDto != null) {
                    if (Integer.parseInt(dataDto.getCmdId()) == AllTickCommand.DEAL_PRICE_MSG.getCmdCode()) {
                        try {
                            AllTickDealQuoteDTO quoteDTO = BeanUtil.toBean(dataDto.getData(), AllTickDealQuoteDTO.class,
                                    CopyOptions.create().setAutoTransCamelCase(true));
                            String tradePairCode = AllTickUtil.getTradePairCode(quoteDTO.getCode());

                            boolean isMarketClose = StrUtil.isEmpty(quoteDTO.getSeq());
                            CurrentPriceRespDTO currentPriceRespDTO =
                                    covertToPriceResp(tradePairCode, quoteDTO, isMarketClose);
                            // 优先处理成交消息
                            process(CandleDataSource.ALLTICK.getValue(), tradePairCode, quoteDTO.getTickTime(),
                                    currentPriceRespDTO, isMarketClose, this::handleDealQuote);

                            // 注意：MQ消息发送统一在handleDealQuote中处理，避免重复发送
                        } catch (IllegalArgumentException e) {
                            log.error("Rocket消费candle消息异常 读取成交报价的data失败: {}", e.getMessage());
                        }
                    } else if (Integer.parseInt(dataDto.getCmdId()) == AllTickCommand.PK_PRICE_MSG.getCmdCode()) {
                        try {
                            AllTickPKQuoteDTO quoteDTO = BeanUtil.toBean(dataDto.getData(), AllTickPKQuoteDTO.class,
                                    CopyOptions.create().setAutoTransCamelCase(true));
                            String tradePairCode = AllTickUtil.getTradePairCode(quoteDTO.getCode());
                            boolean isMarketClose = StrUtil.isEmpty(quoteDTO.getSeq());
                            CandleOrderBookListDTO candleOrderBookListDTO =
                                    AllTickOrderBookConvert.INSTANCE.convert2(quoteDTO);
                            process(CandleDataSource.ALLTICK.getValue(), tradePairCode, quoteDTO.getTickTime(),
                                    candleOrderBookListDTO, isMarketClose, this::handlePKQuote);
                        } catch (IllegalArgumentException e) {
                            log.error("Rocket消费candle消息异常 读取盘口报价的data失败: {}", e.getMessage());
                        }
                    }
                }
            } else {
                log.error("consumer 消息逻辑异常 payload为空 {}", message);
            }
        } else if (message.getDataSource() == CandleDataSource.POLYGON.getValue()) {
            final JSONObject jsonObj = JSONUtil.parseObj(message.getPayload());
            final String evVal = jsonObj.getStr(RESP_KEY_EV);
            String pairKey = null;
            if (RESP_VAL_EV_C.equals(evVal) || RESP_VAL_EV_CAS.equals(evVal)) {
                pairKey = RESP_KEY_PAIR_FOREX;
            } else if (RESP_VAL_EV_XT.equals(evVal) || RESP_VAL_EV_XQ.equals(evVal) || RESP_VAL_EV_XAS.equals(evVal)) {
                pairKey = RESP_KEY_PAIR_CRYPTO;
            }
            if (StrUtil.isNotEmpty(pairKey)) {
                final String pair = jsonObj.getStr(pairKey);
                final String tradePairCode = PolygonUtil.tradeCode(pair, true);
                if (StrUtil.isNotEmpty(tradePairCode)) {
                    CurrentPriceRespDTO priceRespDTO = convertToPriceResp(tradePairCode, jsonObj, false);
                    process(CandleDataSource.POLYGON.getValue(), tradePairCode, String.valueOf(priceRespDTO.getTickTime()), priceRespDTO, false, this::handleDealQuote);

                    // 注意：MQ消息发送统一在handleDealQuote中处理，避免重复发送

                    // 生成委托订单
                    //if (DateUtil.currentSeconds() % 2 == 0) {
                    CandleOrderBookListDTO candleOrderBookListDTO =
                            generateCandleOrderBookListDTO(tradePairCode,
                                    priceRespDTO.getCurrentPrice(),
                                    Optional.ofNullable(priceRespDTO.getVolume()).orElse(new BigDecimal(1)));
                    process(CandleDataSource.POLYGON.getValue(), tradePairCode, String.valueOf(priceRespDTO.getTimestamp()), candleOrderBookListDTO, false, this::handlePKQuote);
                    //}
                } else {
                    log.error("[{}] 无法正确的转换成系统交易对代码", pair);
                }
            } else {
                log.error("无法解析消息中的ticker名称 消息内容:[{}]", jsonObj);
            }

        } else if (message.getDataSource() == CandleDataSource.JIANG_SHAN.getValue()) {
            JiangShanPriceDTO jiangShanPriceDTO = JSONUtil.toBean(message.getPayload(), JiangShanPriceDTO.class);
            if (Objects.nonNull(jiangShanPriceDTO)) {
                // 根据三方代码获取系统交易对代码
                String tradePairCode = JiangShanUtil.getTradePairCode(jiangShanPriceDTO.getSymbol());
                CurrentPriceRespDTO priceRespDTO = covertToPriceResp(tradePairCode, jiangShanPriceDTO, false);
                CandlePriceMessage candlePriceMessage = CandlePriceMessage.builder()
                        .code(priceRespDTO.getTradePairCode()).price(String.valueOf(priceRespDTO.getCurrentPrice()))
                        .volume(String.valueOf(priceRespDTO.getVolume()))
                        .turnover(String.valueOf(priceRespDTO.getCurrentPrice().multiply(priceRespDTO.getVolume())))
                        .timestamp(String.valueOf(priceRespDTO.getTimestamp())).build();
                // 处理成交消息
                process(CandleDataSource.JIANG_SHAN.getValue(), tradePairCode, jiangShanPriceDTO.getTimestamp(),
                        priceRespDTO, JiangShanTradePairStatusHolder.isMarketClosed(tradePairCode), this::handleDealQuote);

                // 注意：MQ消息发送统一在handleDealQuote中处理，避免重复发送

                // 根据当前价格生成委托单信息 -- 3秒生成一次
                if (DateUtil.currentSeconds() % 3 == 0) {
                    CandleOrderBookListDTO candleOrderBookListDTO =
                            generateCandleOrderBookListDTO(tradePairCode, new BigDecimal(jiangShanPriceDTO.getBid()),
                                    new BigDecimal(Optional.ofNullable(jiangShanPriceDTO.getVolume()).orElse("1")));
                    // 处理委托单
                    long tickTime = Long.parseLong(jiangShanPriceDTO.getTimestamp()) + 1L;
                    process(CandleDataSource.JIANG_SHAN.getValue(), tradePairCode, String.valueOf(tickTime),
                            candleOrderBookListDTO, JiangShanTradePairStatusHolder.isMarketClosed(tradePairCode),
                            this::handlePKQuote);
                }

            }

        }
    }

    /**
     * 是否可以有效的tickTime避免重复解析
     *
     * @param code       交易对代码
     * @param tickTime   tick的时间戳
     * @param datasource 数据源
     * @return true: 有效的消息
     */
    private boolean isValidTickTime(String code, long tickTime, int datasource) {
        if (CODE_LAST_TICKTIME_MAP.containsKey(code)) {
            Long lastTickTime = CODE_LAST_TICKTIME_MAP.get(code).getTickTime();
            if (lastTickTime >= tickTime) {
                return false;
            }
        }
        UpdateDto updateDto = new UpdateDto();
        updateDto.setTickTime(tickTime);
        updateDto.setCode(code);
        updateDto.setSource(datasource);
        CODE_LAST_TICKTIME_MAP.put(code, updateDto);
        return true;
    }

    // 通用处理方法
    private <Q> void process(int dataSource, String tradePairCode, String tickTime, Q quoteDTO, Boolean isMarketClose,
                             QuoteHandleFunction<String, Q, Boolean> handler) {
        if (quoteDTO == null) {
            return;
        }
        try {
            long tick = Long.parseLong(tickTime);
            if (isValidTickTime(tradePairCode, tick, dataSource) || isMarketClose) {
                handler.handle(tradePairCode, quoteDTO, isMarketClose);
            }
        } catch (NumberFormatException e) {
            log.error("tickTime的格式错误 {}", tickTime);
        }
    }

    /**
     * 处理成交报价消息对象
     *
     * @param priceDto 消息对象
     */
    private void handleDealQuote(String tradePairCode, CurrentPriceRespDTO priceDto, Boolean isMarketClose) {

        // 使用实时检查，包含时间范围验证
        boolean hasControlPlan =  controlPlanStatusChecker.isControlPlanRunning(tradePairCode);
        if (hasControlPlan) {
            // 有控线计划时：替换成控线数据进行推送
            List<ControlPlanDTO> runningPlans = controlPlanApi.getRunningPlanList();
            ControlPlanDTO controlPlan = runningPlans.stream()
                    .filter(plan -> tradePairCode.equals(plan.getTradePairCode()) && controlPlanStatusChecker.isControlPlanRunningCached(tradePairCode))
                    .findFirst()
                    .orElse(null);

            if (controlPlan != null) {
                // 获取当前时间点附近的控线价格点
                long currentMinuteTimestamp = (System.currentTimeMillis() / 60000) * 60;
                ControlPricePointDTO pricePoint = controlPlanApi.getPricePoint(controlPlan.getId(), currentMinuteTimestamp);

                if (pricePoint != null) {
                    BigDecimal controlPlanPrice = pricePoint.getReferPrice().add(pricePoint.getPriceDiff());
                    //实时价格
                    priceDto.setCurrentPrice(controlPlanPrice);
                    // 重新计算涨跌幅，基于控线计划的开盘价
                    BigDecimal percentage = calculateControlPlanPercentage(controlPlan, controlPlanPrice);
                    //涨跌幅
                    priceDto.setPercentage(percentage);
                    //时间值
                    priceDto.setTimestamp(currentMinuteTimestamp * 1000); // 使用当前分钟时间戳
                    //交易量
                    priceDto.setVolume(pricePoint.getVolume());
                    //成交额
                    priceDto.setTurnover(pricePoint.getTurnover());

                    // 更新TodayKlineHolder，确保前端WebSocket推送包含正确的OHLC数据
                    updateTodayKlineForControlPlan(tradePairCode, controlPlanPrice);
                } else {
                    log.warn("RocketMQ消费者无法获取控线计划价格点，降级使用原始价格: tradePair={}, planId={}",
                            tradePairCode, controlPlan.getId());
                }
            } else {
                log.warn("RocketMQ消费者检测到控线计划，但未找到运行中的计划实例: tradePair={}", tradePairCode);
            }
        }

        // 没有控线计划时：正常更新Redis价格数据
        priceRedisDAO.set(tradePairCode, priceDto);

        // 如果是休市时则不去更新这个价格因为这个价格是系统人工生成的假数据
        if (!isMarketClose) {
            priceRedisDAO.setCloseBackupPrice(tradePairCode, priceDto);
            // 更新休市价格生成器中的基准价格
            marketCloseDataGenerator.updateCloseBasePrice(tradePairCode, priceDto.getCurrentPrice());
        }

        // 修复：没有控线计划时，发送MQ消息触发WebSocket推送
        try {
            CandlePriceMessage mqMessage = CandlePriceMessage.builder()
                .code(tradePairCode)
                .price(String.valueOf(priceDto.getCurrentPrice()))
                .timestamp(String.valueOf(priceDto.getTimestamp()))
                .volume(String.valueOf(priceDto.getVolume()))
                .turnover(String.valueOf(priceDto.getTurnover() != null ? priceDto.getTurnover() : BigDecimal.ZERO))
                .build();
            appPriceProducer.sendCustomTradePriceMessage(mqMessage);
        } catch (Exception e) {
            log.warn("发送真实价格MQ消息失败: tradePair={}, error={}", tradePairCode, e.getMessage());
        }

        long now = DateUtil.currentSeconds();

        // 先获取tradePairCode的所有复制币
        final List<TradePairRespDTO> copyTrades = tradePairApi.getCopyTradeOfReference(tradePairCode);
        final List<ControlPlanDTO> runningPlanList = controlPlanApi.getRunningPlanList();

        final Map<String, ControlPlanDTO> runningPlanMap = runningPlanList.stream()
                .collect(Collectors.toMap(
                    ControlPlanDTO::getTradePairCode,
                    controlPlanDTO -> controlPlanDTO,
                    (existing, replacement) -> {
                        // 如果有重复的交易对，选择ID较大的（最新的）计划
                        log.warn("发现重复的控盘计划，交易对: {}, 保留计划ID: {}, 忽略计划ID: {}",
                                existing.getTradePairCode(),
                                Math.max(existing.getId(), replacement.getId()),
                                Math.min(existing.getId(), replacement.getId()));
                        return existing.getId() > replacement.getId() ? existing : replacement;
                    }
                ));


        for (TradePairRespDTO copyTrade : copyTrades) {
            String copyCode = copyTrade.getCode();

            // 修复：首先检查复制币本身是否有控线计划
            if (isControlPlanRunning(copyCode)) {
                continue; // 复制币有自己的控线计划，不应该被主交易对的价格覆盖
            }

            // 判断复制币此时此刻是否有在控盘（这里是检查复制币的控盘计划）
            final ControlPlanDTO runningPlan = runningPlanMap.get(copyCode);
            if (runningPlan == null) {
                // 复制币没有控盘计划，使用主交易对的价格
                priceRedisDAO.set(copyTrade.getCode(), priceDto);
                continue;
            }

            boolean isRunning = runningPlan.getStartTime() <= now && runningPlan.getEndTime() > now;
            if (isRunning) {
                // 复制币有控盘计划且正在运行，使用控制K线数据
                BigDecimal currentPrice = getCurrentPriceFromControlCandle(copyTrade.getCode(), now);

                if (currentPrice != null) {
                    CurrentPriceRespDTO priceCopied = new CurrentPriceRespDTO();
                    priceCopied.setCurrentPrice(currentPrice);
                    priceCopied.setVolume(priceDto.getVolume()); // 使用原始数据的成交量
                    priceCopied.setTimestamp(now);
                    priceCopied.setTurnover(BigDecimal.ZERO);

                    // 修复：复制币有控线计划时，涨跌幅应该基于控线计划的开盘价计算
                    BigDecimal percentage = calculateControlPlanPercentage(runningPlan, currentPrice);
                    priceCopied.setPercentage(percentage);

                    priceRedisDAO.set(copyTrade.getCode(), priceCopied);
                } else {
                    // 如果没有找到控制K线数据，使用原始价格
                    log.warn("未找到交易对 {} 的控制K线数据，使用原始价格", copyTrade.getCode());
                    priceRedisDAO.set(copyTrade.getCode(), priceDto);
                }
            } else {
                // 复制币的控盘计划不在运行中，使用主交易对的价格
                priceRedisDAO.set(copyTrade.getCode(), priceDto);
            }
        }
    }

    /**
     * 根据实时价格计算交易对的涨跌幅并更新redis中的涨跌幅
     *
     * @param tradePairCode 交易对代码
     * @param currentPrice  实时价格
     */
    private BigDecimal calculateChangePercentage(String tradePairCode, BigDecimal currentPrice) {
        final TodayKlinePriceDTO todayKlinePriceDTO = TodayKlineHolder.get(tradePairCode);

        // 详细的调试信息
        if (currentPrice == null) {
            log.error("涨跌幅计算失败: 当前价格为null, tradePairCode={}", tradePairCode);
            return BigDecimal.valueOf(0, 2);
        }

        if (todayKlinePriceDTO == null) {
            log.warn("涨跌幅计算失败: 未找到今日开盘价数据, tradePairCode={}, 尝试使用当前价格作为基准", tradePairCode);
            // 备用方案：如果没有今日开盘价，返回0%涨跌幅
            return BigDecimal.ZERO;
        }

        if (todayKlinePriceDTO.getOpenPrice() == null) {
            log.warn("涨跌幅计算失败: 今日开盘价为null, tradePairCode={}, todayKlineData={}", tradePairCode, todayKlinePriceDTO);
            return BigDecimal.ZERO;
        }

        try {
            final BigDecimal openPrice = new BigDecimal(todayKlinePriceDTO.getOpenPrice());
            if (openPrice.compareTo(BigDecimal.ZERO) <= 0) {
                log.warn("涨跌幅计算失败: 今日开盘价无效, tradePairCode={}, openPrice={}", tradePairCode, openPrice);
                return BigDecimal.ZERO;
            }

            final BigDecimal change = currentPrice.subtract(openPrice);
            BigDecimal percentage = change.divide(openPrice, 4, RoundingMode.HALF_UP);

            log.debug("涨跌幅计算成功: tradePairCode={}, currentPrice={}, openPrice={}, change={}, percentage={}%",
                     tradePairCode, currentPrice, openPrice, change, percentage.multiply(BigDecimal.valueOf(100)));

            return percentage;
        } catch (NumberFormatException e) {
            log.error("涨跌幅计算异常: 开盘价格式错误, tradePairCode={}, openPrice={}, error={}",
                     tradePairCode, todayKlinePriceDTO.getOpenPrice(), e.getMessage());
        } catch (ArithmeticException e) {
            log.error("涨跌幅计算异常: 算术异常, tradePairCode={}, error={}", tradePairCode, e.getMessage());
        }

        return BigDecimal.ZERO;
    }



    /**
     * 更新TodayKlineHolder中的OHLC数据
     * 确保前端WebSocket推送包含正确的开盘价、最高价、最低价、收盘价
     *
     * @param tradePairCode 交易对代码
     * @param currentPrice 当前价格
     */
    private void updateTodayKlineForControlPlan(String tradePairCode, BigDecimal currentPrice) {
        try {
            TodayKlinePriceDTO todayKline = TodayKlineHolder.get(tradePairCode);

            if (todayKline == null) {
                // 如果没有今日K线数据，创建新的
                // 获取控盘计划信息，使用执行价格作为开盘价
                ControlPlanDTO controlPlan = controlPlanApi.getRunningPlan(tradePairCode);
                BigDecimal openPrice = currentPrice; // 默认使用当前价格

                if (controlPlan != null && controlPlan.getExecPrice() != null) {
                    // 如果有控盘计划且有执行价格，使用执行价格作为开盘价
                    openPrice = controlPlan.getExecPrice();
                }

                todayKline = new TodayKlinePriceDTO();
                todayKline.setTradePairCode(tradePairCode);
                todayKline.setOpenPrice(openPrice.toString());
                todayKline.setHighPrice(currentPrice.toString());
                todayKline.setLowPrice(currentPrice.toString());
                todayKline.setClosePrice(currentPrice.toString());
                todayKline.setVolume("0");
                todayKline.setTurnover("0");
                todayKline.setVolume24H("0");
                todayKline.setTimestamp(String.valueOf(System.currentTimeMillis() / 1000));

            } else {
                // 更新现有的今日K线数据
                BigDecimal highPrice = new BigDecimal(todayKline.getHighPrice());
                BigDecimal lowPrice = new BigDecimal(todayKline.getLowPrice());

                // 更新最高价
                if (currentPrice.compareTo(highPrice) > 0) {
                    todayKline.setHighPrice(currentPrice.toString());
                }

                // 更新最低价
                if (currentPrice.compareTo(lowPrice) < 0) {
                    todayKline.setLowPrice(currentPrice.toString());
                }

                // 始终更新收盘价
                todayKline.setClosePrice(currentPrice.toString());
            }

            // 更新到TodayKlineHolder中
            TodayKlineHolder.updateTodayOpenPriceMap(tradePairCode, todayKline);

        } catch (Exception e) {
            log.error("更新控盘计划今日K线数据失败: tradePair={}, price={}, error={}",
                tradePairCode, currentPrice, e.getMessage(), e);
        }
    }

    /**
     * 计算控线计划的涨跌幅
     * 基于控线计划的开盘价（执行价格）计算涨跌幅
     *
     * @param runningPlan 运行中的控价计划
     * @param currentPrice 当前控线价格
     * @return 涨跌幅百分比
     */
    private BigDecimal calculateControlPlanPercentage(ControlPlanDTO runningPlan, BigDecimal currentPrice) {
        try {

            TodayKlinePriceDTO todayKlinePriceDTO =  TodayKlineHolder.get(runningPlan.getTradePairCode());
            
            String openPriceStr = todayKlinePriceDTO.getOpenPrice();
            BigDecimal openPrice = BigDecimal.ZERO;
            BigDecimal openPriceBigDecimal = new BigDecimal(openPriceStr);
            // 使用控线计划的执行价格作为开盘价基准
            if (openPriceBigDecimal == null || openPriceBigDecimal.compareTo(BigDecimal.ZERO) <= 0) {
                log.warn("控线计划执行价格无效，使用参考价格: planId={}, execPrice={}, referPrice={}",
                        runningPlan.getId(), openPrice, runningPlan.getReferPrice());
                openPrice = runningPlan.getReferPrice();
            }else{
                openPrice = openPriceBigDecimal;
            }

            if (openPrice == null || openPrice.compareTo(BigDecimal.ZERO) <= 0) {
                log.warn("控线计划开盘价格无效，返回0%涨跌幅: planId={}", runningPlan.getId());
                return BigDecimal.ZERO;
            }

            // 计算涨跌幅：(当前价格 - 开盘价格) / 开盘价格 * 100
            BigDecimal change = currentPrice.subtract(openPrice);
            BigDecimal percentage = change.divide(openPrice, 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));

            log.debug("控线计划涨跌幅计算: planId={}, tradePair={}, 开盘价={}, 当前价={}, 涨跌幅={}%",
                    runningPlan.getId(), runningPlan.getTradePairCode(), openPrice, currentPrice, percentage);

            return percentage;
        } catch (Exception e) {
            log.error("控线计划涨跌幅计算失败: planId={}, error={}", runningPlan.getId(), e.getMessage());
            return BigDecimal.ZERO;
        }
    }

    /**
     * 处理盘口报价消息对象
     *
     * @param candleOrderBookListDTO 消息对象
     */
    private void handlePKQuote(String tradePairCode, CandleOrderBookListDTO candleOrderBookListDTO,
                               Boolean isMarketClose) {
        // 检查交易对是否正在执行控价计划
        if (isControlPlanRunning(tradePairCode)) {
            log.debug("交易对 {} 正在执行控价计划，忽略外部盘口数据推送", tradePairCode);
            return;
        }

        // 更新redis中的对应交易对的订单薄信息
        candleOrderBookListDTO.setTradePairCode(tradePairCode);
        orderBookRedisDAO.set(tradePairCode, candleOrderBookListDTO);

        // 设置复制币
        List<TradePairRespDTO> customTradePair = tradePairApi.getCustomTradePair();
        customTradePair = customTradePair.stream()
                .filter(c -> c.getIsCopy() && c.getReferenceCode().equalsIgnoreCase(tradePairCode)).toList();
        for (TradePairRespDTO item : customTradePair) {
            candleOrderBookListDTO.setTradePairCode(item.getCode());
            orderBookRedisDAO.set(item.getCode(), candleOrderBookListDTO);
        }
    }

    /**
     * 获取交易对的最后更新时间
     *
     * @return 更新信息的map
     */
    public static Map<String, UpdateDto> getCodeLastTickTimeMap() {
        return CODE_LAST_TICKTIME_MAP;
    }

    /**
     * 根据当前价格生成委托单数据
     *
     * @param tradePairCode 交易对代码
     * @param currentPrice  当前价格
     * @return 订单薄
     */
    private CandleOrderBookListDTO generateCandleOrderBookListDTO(String tradePairCode, BigDecimal currentPrice,
                                                                  BigDecimal currentVolume) {
        CandleOrderBookListDTO dto = new CandleOrderBookListDTO();
        dto.setTradePairCode(tradePairCode);
        List<CandleOrderBookDTO> bids = Lists.newArrayList();
        List<CandleOrderBookDTO> asks = Lists.newArrayList();
        // 生成
        for (int i = 1; i <= 6; i++) {
            Random random = new Random();
            BigDecimal sellPrice = currentPrice.subtract(
                    (BigDecimal.valueOf(i).divide(BigDecimal.valueOf(random.nextInt(1, 200)), 2, RoundingMode.HALF_UP)));
            BigDecimal buyPrice = currentPrice.add(
                    (BigDecimal.valueOf(i).divide(BigDecimal.valueOf(random.nextInt(1, 200)), 2, RoundingMode.HALF_UP)));
            BigDecimal sellVolume = BigDecimal.ONE;
            BigDecimal buyVolume = BigDecimal.ONE;

            if (currentPrice.compareTo(BigDecimal.ONE) < 0) {
                sellVolume = BigDecimal.valueOf(random.nextInt(1000, 100000));
                buyVolume = BigDecimal.valueOf(random.nextInt(1000, 100000));
            } else if (currentPrice.compareTo(BigDecimal.valueOf(100)) < 0) {
                sellVolume = BigDecimal.valueOf(random.nextInt(100, 10000));
                buyVolume = BigDecimal.valueOf(random.nextInt(100, 10000));
            } else if (currentPrice.compareTo(BigDecimal.valueOf(1000)) < 0) {
                sellVolume = BigDecimal.valueOf(random.nextDouble(1, 1000));
                buyVolume = BigDecimal.valueOf(random.nextDouble(1, 1000));
            } else if (currentPrice.compareTo(BigDecimal.valueOf(2000)) < 0) {
                sellVolume = BigDecimal.valueOf(random.nextDouble(0.01, 100));
                buyVolume = BigDecimal.valueOf(random.nextDouble(0.01, 100));
            } else if (currentPrice.compareTo(BigDecimal.valueOf(5000)) < 0) {
                sellVolume = BigDecimal.valueOf(random.nextDouble(0.01, 50));
                buyVolume = BigDecimal.valueOf(random.nextDouble(0.01, 50));
            } else {
                sellVolume = BigDecimal.valueOf(random.nextDouble(0.01, 10.99));
                buyVolume = BigDecimal.valueOf(random.nextDouble(0.01, 10.99));
            }

            if (sellVolume.compareTo(currentVolume) > 0) {
                sellVolume = sellVolume.divide(BigDecimal.TEN).setScale(2, RoundingMode.HALF_UP);
            }
            if (buyVolume.compareTo(currentVolume) > 0) {
                buyVolume = buyVolume.divide(BigDecimal.TEN).setScale(2, RoundingMode.HALF_UP);
            }
            // sellVolume = BigDecimal.valueOf(random.nextDouble(0.01, currentVolume.doubleValue()));
            // buyVolume = BigDecimal.valueOf(random.nextDouble(0.01, currentVolume.doubleValue()));

            sellVolume = sellVolume.setScale(2, RoundingMode.HALF_UP);
            buyVolume = buyVolume.setScale(2, RoundingMode.HALF_UP);
            CandleOrderBookDTO sellOrderBookDTO = new CandleOrderBookDTO();
            CandleOrderBookDTO buyOrderBookDTO = new CandleOrderBookDTO();
            sellOrderBookDTO.setPrice(String.valueOf(sellPrice));
            sellOrderBookDTO.setVolume(String.valueOf(sellVolume));
            buyOrderBookDTO.setPrice(String.valueOf(buyPrice));
            buyOrderBookDTO.setVolume(String.valueOf(buyVolume));
            bids.add(buyOrderBookDTO);
            asks.add(sellOrderBookDTO);
        }

        dto.setBids(bids);
        dto.setAsks(asks);
        return dto;
    }

    private CurrentPriceRespDTO covertToPriceResp(String tradePairCode, AllTickDealQuoteDTO quoteDTO,
                                                  boolean isMarketClose) {
        long tickTime = Long.parseLong(quoteDTO.getTickTime());
        BigDecimal currentPrice = new BigDecimal(quoteDTO.getPrice());
        // 更新redis中的对应交易对的价格信息
        CurrentPriceRespDTO priceDto = new CurrentPriceRespDTO();
        priceDto.setIsMarketClose(isMarketClose);
        priceDto.setTradePairCode(tradePairCode);
        priceDto.setCurrentPrice(currentPrice);
        priceDto.setVolume(new BigDecimal(quoteDTO.getVolume()));
        priceDto.setTickTime(tickTime);
        priceDto.setTimestamp(System.currentTimeMillis());
        // 计算涨跌幅
        final BigDecimal changePercentage = calculateChangePercentage(tradePairCode, currentPrice);
        priceDto.setPercentage(changePercentage);
        return priceDto;
    }

    private CurrentPriceRespDTO covertToPriceResp(String tradePairCode, JiangShanPriceDTO jiangShanPriceDTO,
                                                  boolean isMarketClose) {
        long tickTime = Long.parseLong(jiangShanPriceDTO.getTimestamp());
        CurrentPriceRespDTO priceDto = new CurrentPriceRespDTO();
        priceDto.setTradePairCode(tradePairCode);
        priceDto.setCurrentPrice(new BigDecimal(jiangShanPriceDTO.getBid()));
        priceDto.setVolume(new BigDecimal(jiangShanPriceDTO.getVolume()));
        priceDto.setTickTime(tickTime);
        priceDto.setTimestamp(System.currentTimeMillis());
        priceDto.setPercentage(
                new BigDecimal(jiangShanPriceDTO.getPercent()).divide(new BigDecimal(100), 4, RoundingMode.HALF_UP));
        priceDto.setIsMarketClose(isMarketClose);
        return priceDto;
    }

    private CurrentPriceRespDTO convertToPriceResp(String tradePairCode, JSONObject jsonObj, boolean isMarketClose) {
        CurrentPriceRespDTO priceDto = new CurrentPriceRespDTO();
        final String evVal = jsonObj.getStr(PolygonConstants.RESP_KEY_EV);
        String priceKey = "b";
        String volumeKey = "s";
        if (RESP_VAL_EV_XT.equals(evVal)) {
            priceKey = "p";
            volumeKey = "s";
        }
        final long ts = jsonObj.getLong("t");
        priceDto.setTradePairCode(tradePairCode);
        priceDto.setCurrentPrice(jsonObj.getBigDecimal(priceKey));
        priceDto.setVolume(Objects.requireNonNullElse(jsonObj.getBigDecimal(volumeKey), BigDecimal.ZERO));
        priceDto.setTurnover(BigDecimal.ZERO);
        priceDto.setTimestamp(ts);
        priceDto.setTickTime(ts);
        priceDto.setIsMarketClose(isMarketClose);
        BigDecimal changePercentage = calculateChangePercentage(tradePairCode, priceDto.getCurrentPrice());
        priceDto.setPercentage(changePercentage);
        log.debug("Polygon 价格信息:[{}]", priceDto);
        return priceDto;
    }

    /**
     * 检查交易对是否正在执行控价计划
     * 使用统一的ControlPlanStatusChecker服务
     *
     * @param tradePairCode 交易对代码
     * @return true-正在执行控价计划，false-没有执行控价计划
     */
    private boolean isControlPlanRunning(String tradePairCode) {
        // 使用统一的控价计划状态检查器（缓存版本，适合高频调用）
        return controlPlanStatusChecker.isControlPlanRunningCached(tradePairCode);
    }

    /**
     * 更新控价计划状态缓存
     */
    private void updateControlPlanStatusCache() {
        try {
            long now = System.currentTimeMillis();
            Map<String, Long> newCache = new ConcurrentHashMap<>();

            // 获取所有运行中的控价计划
            List<ControlPlanDTO> runningPlans = controlPlanApi.getRunningPlanList();
            if (runningPlans != null && !runningPlans.isEmpty()) {
                for (ControlPlanDTO plan : runningPlans) {
                    // 检查plan对象和关键字段是否为null
                    if (plan != null && plan.getStartTime() != null && plan.getEndTime() != null && plan.getTradePairCode() != null) {
                        // 只缓存正在运行的计划
                        if (plan.getStartTime() <= now && plan.getEndTime() > now) {
                            newCache.put(plan.getTradePairCode(), plan.getEndTime());
                        }
                    }
                }
            }

            // 原子性更新缓存
            // controlPlanStatusCache = newCache; // This line was removed as per the new_code, as the variable is no longer declared.
            log.debug("更新控价计划状态缓存，当前运行中的计划数量: {}", newCache.size());

        } catch (Exception e) {
            log.warn("更新控价计划状态缓存失败: {}", e.getMessage());
        }
    }

    /**
     * 从控制K线数据中获取当前价格
     *
     * @param tradePairCode 交易对代码
     * @param timestamp 时间戳
     * @return 当前价格，如果没有找到则返回null
     */
    private BigDecimal getCurrentPriceFromControlCandle(String tradePairCode, long timestamp) {
        try {
            // 获取当前分钟的控制K线数据
            long minuteTimestamp = (timestamp / 60) * 60; // 转换为分钟级时间戳

            // 这里需要调用控制K线服务获取数据
            // 由于没有直接的API，我们暂时返回null，让调用方使用原始价格
            // TODO: 实现从控制K线数据库中查询当前价格的逻辑

            return null;
        } catch (Exception e) {
            log.error("获取控制K线价格失败: tradePairCode={}, timestamp={}, error={}",
                    tradePairCode, timestamp, e.getMessage());
            return null;
        }
    }

    /**
     * 安全计算控价计划的价格，确保不为负数
     * @deprecated 不再使用价格计算，直接使用控制K线数据
     */
    @Deprecated
    private BigDecimal calculateSafeControlPrice(BigDecimal referPrice, BigDecimal diffPrice, BigDecimal priceDiff) {
        BigDecimal calculatedPrice = referPrice.add(diffPrice).add(priceDiff);

        // 如果计算出的价格为负数或零，调整为参考价格的0.1%
        if (calculatedPrice.compareTo(BigDecimal.ZERO) <= 0) {
            BigDecimal minPositivePrice = referPrice.multiply(new BigDecimal("0.001"));
            System.err.println(String.format("MQ消费者检测到负数价格，已调整: 参考价格=%s, 执行差值=%s, 价格差值=%s, 计算价格=%s, 调整后价格=%s",
                    referPrice, diffPrice, priceDiff, calculatedPrice, minPositivePrice));
            return minPositivePrice;
        }

        return calculatedPrice;
    }

}